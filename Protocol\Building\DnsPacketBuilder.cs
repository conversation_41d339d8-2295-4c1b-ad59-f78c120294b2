using System.Buffers.Binary;
using DnsServer.Protocol;

namespace DnsServer.Protocol.Building;

public class DnsPacketBuilder
{
    private readonly byte[] _buffer;
    private int _offset;

    public DnsPacketBuilder(int size = 512)
    {
        _buffer = new byte[size];
        _offset = 0;
    }

    public ReadOnlyMemory<byte> Build(DnsMessage message)
    {
        BuildHeader(message.Header);
        // In future stages, we will call BuildQuestion and BuildAnswer here.
        return _buffer.AsMemory(0, _offset);
    }

    private void BuildHeader(DnsHeader header)
    {
        // Ensure we have enough space for the header
        if (_buffer.Length < 12)
        {
            throw new ArgumentException("Buffer is too small for DNS header.");
        }

        // Section 4.1.1 of RFC 1035
        // Write Packet ID (16 bits)
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), header.PacketId);
        _offset += 2;

        // Write Flags (16 bits total)
        // First byte of flags
        byte flags1 = 0;
        if (header.IsResponse) flags1 |= 1 << 7; // QR
        flags1 |= (byte)(header.OpCode << 3);    // OPCODE
        if (header.AuthoritativeAnswer) flags1 |= 1 << 2; // AA
        if (header.Truncation) flags1 |= 1 << 1;          // TC
        if (header.RecursionDesired) flags1 |= 1;         // RD
        _buffer[_offset++] = flags1;

        // Second byte of flags
        byte flags2 = 0;
        if (header.RecursionAvailable) flags2 |= 1 << 7; // RA
        // Z is reserved (3 bits), should be 0
        flags2 |= (byte)header.ResponseCode;             // RCODE
        _buffer[_offset++] = flags2;

        // Write QDCOUNT (16 bits)
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), header.QuestionCount);
        _offset += 2;

        // Write ANCOUNT (16 bits)
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), header.AnswerRecordCount);
        _offset += 2;

        // Write NSCOUNT (16 bits)
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), header.AuthorityRecordCount);
        _offset += 2;

        // Write ARCOUNT (16 bits)
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), header.AdditionalRecordCount);
        _offset += 2;
    }
}
