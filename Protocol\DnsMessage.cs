namespace DnsServer.Protocol;

public class DnsMessage
{
    public DnsHeader Header { get; set; }

    // For now, these lists will be empty. We will add to them in later stages.
    // public List<DnsQuestion> Questions { get; set; }
    // public List<DnsAnswer> Answers { get; set; }

    public DnsMessage()
    {
        Header = new DnsHeader();
        // Questions = new List<DnsQuestion>();
        // Answers = new List<DnsAnswer>();
    }
}
