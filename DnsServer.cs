using System.Net;
using System.Net.Sockets;
using DnsServer.Protocol;
using DnsServer.Protocol.Building;

namespace DnsServer;

public class DnsServer
{
    private const int MaxUdpSize = 512; // As per DNS standards for non-EDNS
    private readonly IPEndPoint _listenEndPoint = new(IPAddress.Any, 2053);
    private readonly IPEndPoint? _resolverEndPoint;

    public DnsServer(IPEndPoint? resolverEndPoint)
    {
        _resolverEndPoint = resolverEndPoint;
    }

    public async Task StartAsync(CancellationToken token)
    {
        // Create and configure the main listening socket
        using var socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
        socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
        socket.Bind(_listenEndPoint);

        Console.WriteLine($"DNS Server listening on {_listenEndPoint}");

        // A single buffer is allocated once and reused for all receive operations
        var buffer = new byte[MaxUdpSize];
        
        // The remote endpoint for the received packet will be stored here
        var remoteEndPoint = new IPEndPoint(IPAddress.Any, 0);

        while (!token.IsCancellationRequested)
        {
            try
            {
                // Asynchronously wait for an incoming packet
                var result = await socket.ReceiveFromAsync(buffer, SocketFlags.None, remoteEndPoint, token);
                var receivedBytes = buffer.AsMemory(0, result.ReceivedBytes);

                // Offload processing to a separate task to keep the receive loop available
                _ = ProcessPacketAsync(socket, receivedBytes, (IPEndPoint)result.RemoteEndPoint, token);
            }
            catch (SocketException ex) when (ex.SocketErrorCode == SocketError.ConnectionReset)
            {
                // This is normal for UDP when a client closes - just continue listening
                continue;
            }
            catch (SocketException ex) when (ex.SocketErrorCode == SocketError.MessageSize)
            {
                // Packet was larger than our buffer - log and continue
                Console.WriteLine($"Received oversized packet (larger than {MaxUdpSize} bytes) - ignoring");
                continue;
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation token is triggered
                break;
            }
        }
    }

    private async Task ProcessPacketAsync(Socket socket, ReadOnlyMemory<byte> queryBytes, IPEndPoint remoteEp, CancellationToken token)
    {
        try
        {
            Console.WriteLine($"Received {queryBytes.Length} bytes from {remoteEp}. Building DNS header response.");

            // Create the DNS message and header
            var responseMessage = new DnsMessage();
            var header = responseMessage.Header;

            // Set the hardcoded values for the Stage 2 response
            header.PacketId = 1234;
            header.IsResponse = true; // This is a response
            header.OpCode = 0; // Standard query
            header.AuthoritativeAnswer = false;
            header.Truncation = false;
            header.RecursionDesired = false; // We don't act on this yet
            header.RecursionAvailable = false; // We don't support recursion
            header.ResponseCode = 0; // No error
            header.QuestionCount = 0;
            header.AnswerRecordCount = 0;
            header.AuthorityRecordCount = 0;
            header.AdditionalRecordCount = 0;

            // Use the builder to construct the packet
            var builder = new DnsPacketBuilder();
            var responseBytes = builder.Build(responseMessage);

            // Send the response back to the client
            await socket.SendToAsync(responseBytes, SocketFlags.None, remoteEp, token);
            Console.WriteLine($"Sent {responseBytes.Length}-byte header to {remoteEp}.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing packet from {remoteEp}: {ex.Message}");
        }
    }
}
